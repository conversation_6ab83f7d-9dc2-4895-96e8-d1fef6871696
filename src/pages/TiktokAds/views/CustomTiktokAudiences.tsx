import { useState } from 'react';
import DataTable from '@/components/table/DataTable';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { ICustomAudienceResponse } from '@/types/tiktok';
import FilterPanel from '@/pages/TiktokAds/components/FilterPanel';
import audienceCol from '@/pages/TiktokAds/components/Column/audienceCol';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { formatDateYYYYMMDD } from '@/utils/helper';

interface ICustomAudiences {
  filterPayload: TFilterAudienceTiktok;
  setFilterPayload: (value: TFilterAudienceTiktok) => void;
  loading?: boolean;
  tiktokCustomAudience: ICustomAudienceResponse;
}

const CustomTiktokAudiences = ({
  filterPayload,
  setFilterPayload,
  loading,
  tiktokCustomAudience,
  setTiktokCustomAudience,
}: ICustomAudiences) => {
  const { adsAccount } = useTiktokContext();

  const [idActive, setIdActive] = useState<number>(0);
  const [listAudiences, setListAudiences] = useState<IAudienceDetail[]>(tiktokCustomAudience.items);

  useEffect(() => {
    if (!!tiktokCustomAudience.items.length) {
      setListAudiences(tiktokCustomAudience.items);
    }
  }, [tiktokCustomAudience.items]);

  useEffect(() => {
    if (idActive > 0 && listAudiences.find((item) => item.total_records === 0 || item.status === 'PENDING')) {
      get({
        endpoint: ENDPOINTS.custom_audience.detail(idActive),
      }).then((res) => {
        const dataAudience = res?.data?.data as unknown as IAudienceDetail;
        setListAudiences((prev) =>
          prev.map((item) =>
            item.job_id === dataAudience.job_id ? dataAudience : item,
          ),
        );
        // Update the main audience data as well
        setTiktokCustomAudience({
          ...tiktokCustomAudience,
          items: listAudiences.map((item) =>
            item.job_id === dataAudience.job_id ? dataAudience : item,
          ),
        });
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idActive]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({
            ...filterPayload,
            ...value,
            date_created_from: formatDateYYYYMMDD(value?.date_created_from ?? '', '-'),
            date_created_to: formatDateYYYYMMDD(value?.date_created_to ?? '', '-'),
          });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={listAudiences}
        columns={audienceCol({ act: adsAccount?.ad_account_id || '', setIdActive })}
        total={tiktokCustomAudience.count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
      />
    </div>
  );
};
export default CustomTiktokAudiences;
